# VishnoRex Staff Attendance System

A comprehensive web-based staff attendance management system built with Flask.

## 🚀 Features

- **Multi-School Support**: Manage multiple schools/institutions from one system
- **Role-Based Access**: Company Admin, School Admin, and Staff roles
- **Attendance Tracking**: Clock in/out functionality with time tracking
- **Leave Management**: Apply for and approve leave applications
- **Staff Management**: Add, edit, and manage staff profiles
- **Reporting**: Export attendance and staff reports in CSV format
- **File Uploads**: Support for staff photos and school logos
- **Security**: CSRF protection, secure password hashing, file upload validation

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vishnorex-attendance
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up demo data (optional)**
   ```bash
   python setup_demo_data.py
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the application**
   Open your browser and go to `http://127.0.0.1:5000`

## 🔐 Demo Login Credentials

After running `setup_demo_data.py`:

- **Company Admin**: `company_admin` / `admin123`
- **School Admin**: `school_admin` / `admin123`  
- **Staff**: `STAFF001`, `STAFF002`, or `STAFF003` / `staff123`

## 📁 Project Structure

```
├── app.py                 # Main Flask application
├── database.py           # Database configuration and initialization
├── requirements.txt      # Python dependencies
├── setup_demo_data.py   # Demo data setup script
├── test_app.py          # Unit tests
├── templates/           # HTML templates
├── static/             # CSS, JS, and uploaded files
└── vishnorex.db        # SQLite database file
```

## 🔧 Configuration

### Environment Variables

- `SECRET_KEY`: Flask secret key (auto-generated if not set)

### File Upload Settings

- **Allowed file types**: PNG, JPG, JPEG, GIF
- **Maximum file size**: 16MB
- **Upload directories**: 
  - Staff photos: `static/uploads/`
  - School logos: `static/school_logos/`

## 🧪 Testing

Run the test suite:
```bash
python test_app.py
```

## 🔒 Security Features

- **CSRF Protection**: All forms protected against CSRF attacks
- **Password Hashing**: Secure password storage using Werkzeug
- **File Upload Validation**: Restricted file types and size limits
- **SQL Injection Prevention**: Parameterized queries throughout
- **Session Management**: Secure session handling

## 📊 Database Schema

The system uses SQLite with the following main tables:
- `schools` - Institution information
- `company_admins` - System administrators
- `admins` - School administrators  
- `staff` - Staff members
- `attendance` - Daily attendance records
- `leave_applications` - Leave requests and approvals

## 🚀 Deployment

For production deployment:

1. Set a strong `SECRET_KEY` environment variable
2. Use a production WSGI server (e.g., Gunicorn)
3. Configure a reverse proxy (e.g., Nginx)
4. Consider using PostgreSQL instead of SQLite
5. Set up proper backup procedures

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.
