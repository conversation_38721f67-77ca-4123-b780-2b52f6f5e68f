document.addEventListener('DOMContentLoaded', function() {
    // Initialize attendance chart with real data
    const ctx = document.getElementById('attendanceChart')?.getContext('2d');
    if (ctx) {
        fetch('/get_attendance_summary')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const attendanceChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Present', 'Absent', 'Late', 'Leave'],
                            datasets: [{
                                data: [data.present, data.absent, data.late, data.leave],
                                backgroundColor: [
                                    '#198754',
                                    '#dc3545',
                                    '#ffc107',
                                    '#0dcaf0'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });

                    // Update counts
                    document.getElementById('presentDays').textContent = data.present;
                    document.getElementById('absentDays').textContent = data.absent;
                    document.getElementById('lateDays').textContent = data.late;
                    document.getElementById('leaveDays').textContent = data.leave;
                }
            });
    }

    // Biometric authentication
    const startAuthBtn = document.getElementById('startAuthBtn');
    const fingerprintScanner = document.getElementById('fingerprintScanner');
    const faceScanner = document.getElementById('faceScanner');
    const authStatus = document.getElementById('authStatus');
    const attendanceStatus = document.getElementById('attendanceStatus');
    const markInBtn = document.getElementById('markInBtn');
    const markOutBtn = document.getElementById('markOutBtn');

    let authStep = 0; // 0: not started, 1: fingerprint, 2: face

    startAuthBtn?.addEventListener('click', function() {
        authStep = 1;
        startAuthBtn.disabled = true;
        authStatus.innerHTML = '<div class="alert alert-info">Starting biometric authentication...</div>';

        // Simulate fingerprint scan
        setTimeout(() => {
            fingerprintScan();
        }, 1000);
    });

    function fingerprintScan() {
        authStatus.innerHTML = '<div class="alert alert-info">Scanning fingerprint... Please place your finger on the scanner</div>';

        setTimeout(() => {
            authStatus.innerHTML = '<div class="alert alert-success">Fingerprint verified successfully</div>';
            fingerprintScanner.style.display = 'none';
            faceScanner.style.display = 'block';
            authStep = 2;
            faceRecognition();
        }, 2000);
    }

    function faceRecognition() {
        authStatus.innerHTML = '<div class="alert alert-info">Starting face recognition... Please look at the camera</div>';

        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                    const video = document.getElementById('faceVideo');
                    video.srcObject = stream;

                    setTimeout(() => {
                        const canvas = document.getElementById('canvasElement');
                        const context = canvas.getContext('2d');
                        context.drawImage(video, 0, 0, canvas.width, canvas.height);

                        stream.getTracks().forEach(track => track.stop());
                        video.srcObject = null;

                        completeAuthentication();
                    }, 3000);
                });
        }
    }

    function completeAuthentication() {
        authStatus.innerHTML = '<div class="alert alert-success">Biometric authentication successful!</div>';
        faceScanner.style.display = 'none';
        attendanceStatus.innerHTML = '<h4>Status: <span class="text-success">Ready to Mark Attendance</span></h4>';
        markInBtn.disabled = false;
    }

    // Initialize calendar
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: function (fetchInfo, successCallback, failureCallback) {
                fetch(`/get_staff_attendance?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                    .then(response => response.json())
                    .then(data => {
                        const events = data.attendance.map(record => {
                            let color;
                            if (record.status === 'present') color = '#198754';
                            else if (record.status === 'absent') color = '#dc3545';
                            else if (record.status === 'late') color = '#ffc107';
                            else if (record.status === 'leave') color = '#0dcaf0';

                            return {
                                title: `${record.status}${record.time_in ? ' (' + record.time_in + ')' : ''}`,
                                start: record.date,
                                allDay: true,
                                backgroundColor: color,
                                borderColor: color
                            };
                        });

                        if (data.holidays) {
                            data.holidays.forEach(holiday => {
                                events.push({
                                    title: holiday.name,
                                    start: holiday.date,
                                    allDay: true,
                                    backgroundColor: '#6c757d',
                                    borderColor: '#6c757d'
                                });
                            });
                        }

                        successCallback(events);
                    })
                    .catch(error => {
                        console.error('Error fetching attendance:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function (info) {
                alert('Attendance: ' + info.event.title);
            }
        });
        calendar.render();
    }

    // Mark attendance
    markInBtn?.addEventListener('click', function () {
        fetch('/mark_attendance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=in'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    attendanceStatus.innerHTML = '<h4>Status: <span class="text-success">Present</span></h4>';
                    markInBtn.disabled = true;
                    markOutBtn.disabled = false;
                } else {
                    alert(data.error || 'Failed to mark attendance');
                }
            });
    });

    markOutBtn?.addEventListener('click', function () {
        fetch('/mark_attendance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=out'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    attendanceStatus.innerHTML = '<h4>Status: <span class="text-success">Present (Marked Out)</span></h4>';
                    markOutBtn.disabled = true;
                } else {
                    alert(data.error || 'Failed to mark attendance');
                }
            });
    });

    // Apply leave
    const submitLeave = document.getElementById('submitLeave');
    submitLeave?.addEventListener('click', function () {
        const leaveType = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reason = document.getElementById('leaveReason').value;

        if (!leaveType || !startDate || !endDate || !reason) {
            alert('Please fill all fields');
            return;
        }

        fetch('/apply_leave', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `leave_type=${leaveType}&start_date=${startDate}&end_date=${endDate}&reason=${encodeURIComponent(reason)}`
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Leave application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyLeaveModal')).hide();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit leave application');
                }
            });
    });

    // Updated download report with date selection
// Updated download report with date selection
document.getElementById('downloadReportBtn')?.addEventListener('click', function() {
    const startDate = prompt('Enter start date (YYYY-MM-DD):');
    if (!startDate) return;

    const endDate = prompt('Enter end date (YYYY-MM-DD):');
    if (!endDate) return;

    fetch(`/export_staff_report?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_report_${startDate}_to_${endDate}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        });
});
});
