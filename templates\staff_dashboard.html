<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Dashboard - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="#">VishnoRex</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="#">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#applyLeaveModal">Apply Leave</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> {{ session.full_name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#">Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <!-- Today's Attendance -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5>Today's Attendance</h5>
                </div>
                <div class="card-body text-center">
                    <div id="attendanceStatus" class="mb-3">
                        <h4>Status: Not Marked</h4>
                    </div>
                    <div id="biometricAuth">
                        <div class="mb-3">
                            <video id="videoElement" width="320" height="240" autoplay style="display:none;"></video>
                            <canvas id="canvasElement" width="320" height="240" style="display:none;"></canvas>
                            <div id="fingerprintScanner" class="mb-3">
                                <p>Place your finger on the scanner</p>
                                <div id="fingerprintPlaceholder" style="width:200px; height:200px; margin:0 auto; background-color:#eee; border-radius:50%;"></div>
                            </div>
                            <div id="faceScanner" class="mb-3" style="display:none;">
                                <p>Look at the camera for face recognition</p>
                                <video id="faceVideo" width="320" height="240" autoplay></video>
                            </div>
                        </div>
                        <button id="startAuthBtn" class="btn btn-primary">
                            <i class="bi bi-fingerprint"></i> Start Authentication
                        </button>
                        <div id="authStatus" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <!-- My Leave Applications -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5>My Leave Applications</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Leave Type</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Reason</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in leaves %}
                                <tr>
                                    <td>{{ leave.leave_type }}</td>
                                    <td>{{ leave.start_date }}</td>
                                    <td>{{ leave.end_date }}</td>
                                    <td>{{ leave.reason }}</td>
                                    <td>
                                        <span class="badge bg-{% if leave.status == 'approved' %}success{% elif leave.status == 'rejected' %}danger{% else %}warning{% endif %}">
                                            {{ leave.status }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Attendance Calendar -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5>Attendance Calendar</h5>
                </div>
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-outline-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#applyLeaveModal">
                        <i class="bi bi-calendar-plus"></i> Apply Leave
                    </button>
                    <button class="btn btn-outline-secondary w-100" id="downloadReportBtn">
                        <i class="bi bi-file-earmark-text"></i> Download Report
                    </button>
                </div>
            </div>

            <!-- Attendance Summary -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5>Attendance Summary ({{ today.strftime('%B %Y') }})</h5>
                </div>
                <div class="card-body">
                    <canvas id="attendanceChart" width="100%" height="200"></canvas>
                    <div class="mt-3">
                        <p><span class="badge bg-success me-2">&nbsp;</span> Present: <span id="presentDays">0</span> days</p>
                        <p><span class="badge bg-danger me-2">&nbsp;</span> Absent: <span id="absentDays">0</span> days</p>
                        <p><span class="badge bg-warning me-2">&nbsp;</span> Late: <span id="lateDays">0</span> days</p>
                        <p><span class="badge bg-info me-2">&nbsp;</span> Leave: <span id="leaveDays">0</span> days</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Apply Leave Modal -->
<div class="modal fade" id="applyLeaveModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Apply for Leave</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="leaveForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="mb-3">
                        <label for="leaveType" class="form-label">Leave Type</label>
                        <select class="form-select" id="leaveType" required>
                            <option value="" selected disabled>Select leave type</option>
                            <option value="CL">Casual Leave (CL)</option>
                            <option value="SL">Sick Leave (SL)</option>
                            <option value="EL">Earned Leave (EL)</option>
                            <option value="ML">Maternity Leave (ML)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="startDate" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="startDate" required>
                    </div>
                    <div class="mb-3">
                        <label for="endDate" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="endDate" required>
                    </div>
                    <div class="mb-3">
                        <label for="leaveReason" class="form-label">Reason</label>
                        <textarea class="form-control" id="leaveReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitLeave">Submit</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="../static/js/staff_dashboard.js"></script>
</body>
</html>