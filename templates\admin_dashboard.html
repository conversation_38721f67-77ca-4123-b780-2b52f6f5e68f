<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">VishnoRex - Admin</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#addStaffModal">Add Staff</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#biometricDeviceModal">Biometric Device</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ session.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5>Today's Attendance ({{ today.strftime('%d %b %Y') }})</h5>
                        <div>
                            <span class="badge bg-success">{{ attendance_summary.present }} Present</span>
                            <span class="badge bg-danger ms-2">{{ attendance_summary.absent }} Absent</span>
                            <span class="badge bg-warning ms-2">{{ attendance_summary.late }} Late</span>
                            <span class="badge bg-info ms-2">{{ attendance_summary.on_leave }} On Leave</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="staffSearch" placeholder="Search staff...">
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Staff ID</th>
                                        <th>Name</th>
                                        <th>Department</th>
                                        <th>Time In</th>
                                        <th>Time Out</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for member in staff %}
                                    <tr>
                                        <td>{{ member.staff_id }}</td>
                                        <td>
                                            <a href="#" class="staff-profile" data-staff-id="{{ member.id }}">
                                                {{ member.full_name }}
                                            </a>
                                        </td>
                                        <td>{{ member.department }}</td>
                                        <td>--:--:--</td>
                                        <td>--:--:--</td>
                                        <td>
                                            <span class="badge bg-secondary">Not Marked</span>
                                        </td>
                                        <td>
    <a href="{{ url_for('staff_profile', id=member.id) }}" class="btn btn-sm btn-outline-primary">
        <i class="bi bi-eye"></i> View
    </a>
</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5>Pending Leave Applications</h5>
                    </div>
                    <div class="card-body">
                        {% if pending_leaves %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Staff Name</th>
                                        <th>Leave Type</th>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Reason</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for leave in pending_leaves %}
                                    <tr>
                                        <td>{{ leave.full_name }}</td>
                                        <td>{{ leave.leave_type }}</td>
                                        <td>{{ leave.start_date }}</td>
                                        <td>{{ leave.end_date }}</td>
                                        <td>{{ leave.reason }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-success approve-btn" data-leave-id="{{ leave.id }}">
                                                <i class="bi bi-check-circle"></i> Approve
                                            </button>
                                            <button class="btn btn-sm btn-danger reject-btn" data-leave-id="{{ leave.id }}">
                                                <i class="bi bi-x-circle"></i> Reject
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">No pending leave applications</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5>Staff Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" 
                               data-bs-toggle="modal" data-bs-target="#addStaffModal">
                                <span><i class="bi bi-person-plus"></i> Add New Staff</span>
                                <i class="bi bi-chevron-right"></i>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="exportStaffBtn">
                                <span><i class="bi bi-file-earmark-excel"></i> Export Staff Data</span>
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Staff Profile View (hidden by default) -->
                <div class="card" id="staffProfileCard" style="display: none;">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5>Staff Profile</h5>
                        <button class="btn btn-sm btn-danger" id="deleteStaffBtn">
                            <i class="bi bi-trash"></i> Delete Staff
                        </button>
                    </div>
                    <div class="card-body" id="staffProfileContent">
                        <!-- Content will be loaded dynamically -->
                    </div>
                    <div class="card-footer">
                        <div id="staffCalendar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Add Staff Modal -->
<div class="modal fade" id="addStaffModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Add New Staff Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="staffForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="mb-3">
                        <label for="staffId" class="form-label">Staff ID</label>
                        <input type="text" class="form-control" id="staffId" required>
                    </div>
                    <div class="mb-3">
                        <label for="fullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="fullName" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="photo" class="form-label">Staff Photo</label>
                        <input type="file" class="form-control" id="photo" accept="image/*">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone">
                    </div>
                    <div class="mb-3">
                        <label for="department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department">
                    </div>
                    <div class="mb-3">
                        <label for="position" class="form-label">Position</label>
                        <input type="text" class="form-control" id="position">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveStaff">Save</button>
            </div>
        </div>
    </div>
</div>

    <!-- Biometric Enrollment Modal -->
    <div class="modal fade" id="biometricModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">Biometric Enrollment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="enrollmentProgress">
                        <p>Please scan your fingerprint and face for verification (5 times each)</p>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                        </div>
                        <div class="mb-3">
                            <video id="videoElement" width="320" height="240" autoplay></video>
                            <canvas id="canvasElement" width="320" height="240" style="display:none;"></canvas>
                        </div>
                        <div id="fingerprintScanner">
                            <p>Place your finger on the scanner</p>
                            <div id="fingerprintPlaceholder" style="width:200px; height:200px; margin:0 auto; background-color:#eee; border-radius:50%;"></div>
                        </div>
                        <div id="enrollmentStatus" class="mt-3"></div>
                    </div>
                    <div id="enrollmentComplete" style="display:none;">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill"></i> Biometric enrollment completed successfully!
                        </div>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Biometric Device Management Modal -->
    <div class="modal fade" id="biometricDeviceModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">ZK Biometric Device Management</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Device Configuration</h6>
                            <div class="mb-3">
                                <label for="deviceIP" class="form-label">Device IP Address</label>
                                <input type="text" class="form-control" id="deviceIP" value="*************">
                            </div>
                            <div class="mb-3">
                                <label for="devicePort" class="form-label">Port</label>
                                <input type="number" class="form-control" id="devicePort" value="4370">
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-info" id="testConnectionBtn">
                                    <i class="bi bi-wifi"></i> Test Connection
                                </button>
                                <button type="button" class="btn btn-success" id="syncAttendanceBtn">
                                    <i class="bi bi-arrow-clockwise"></i> Sync Attendance
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Device Status</h6>
                            <div id="deviceStatus" class="alert alert-secondary">
                                <i class="bi bi-info-circle"></i> Click "Test Connection" to check device status
                            </div>
                            <div id="syncResults" style="display: none;">
                                <h6>Last Sync Results</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Total Records:</span>
                                        <span id="totalRecords">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>SQLite Synced:</span>
                                        <span id="sqliteSynced">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>MySQL Synced:</span>
                                        <span id="mysqlSynced">-</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-12">
                            <h6>Device Users</h6>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="loadUsersBtn">
                                <i class="bi bi-people"></i> Load Users from Device
                            </button>
                            <div id="deviceUsers" class="mt-3" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>User ID</th>
                                                <th>Name</th>
                                                <th>Privilege</th>
                                                <th>Group</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
    <script src="../static/js/admin_dashboard.js"></script>
</body>
</html>